import os

from dotenv import load_dotenv
from pydantic_settings import BaseSettings

load_dotenv()


class Config(BaseSettings):
    app_name: str = "Incident Management API"
    app_version: str = "1.0.0"
    debug: bool = False
    db_user: str = os.getenv("POSTGRES_USER", "")
    db_password: str = os.getenv("POSTGRES_PASSWORD", "")
    db_host: str = os.getenv("POSTGRES_HOST", "")
    db_port: int = int(os.getenv("POSTGRES_PORT", 5432))
    db_name: str = os.getenv("POSTGRES_DB", "")

    # CORS
    allowed_origins: list[str] = [
        "http://localhost:5173",
        "http://localhost:2000",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:2000",
    ]
    allowed_methods: list[str] = ["*"]
    allowed_headers: list[str] = ["*"]
    allow_credentials: bool = True

    @property
    def database_url(self) -> str:
        if not self.db_user or not self.db_password or not self.db_host or not self.db_name:
            raise ValueError("Database configuration is incomplete")

        return f"postgresql+psycopg://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_name}"


config = Config()
