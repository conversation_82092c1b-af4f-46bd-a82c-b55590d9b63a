"""
Service Layer for Data Bank
===========================

This module contains the business logic for the Data Bank feature, acting as an
intermediary between the API controllers and the VectorSearchService.
It provides a minimal interface for vector operations while delegating all
data processing to the VectorSearchService.

The functions in this service are responsible for:
- Taking incident and document IDs from the controller.
- Providing database sessions to the VectorSearchService.
- Using the `VectorSearchService` for all vector operations.
- Handling any errors that arise from these downstream services and
  propagating them correctly.

This separation of concerns ensures that the controller is lean and only
handles API-level tasks, while the VectorSearchService handles all the
technical complexity including data retrieval, embedding generation, and vector storage.

"""

from typing import Any, Dict, List
from uuid import UUID

from database.core import DbSession
from utils.logger import get_service_logger
from vector_db.search_service import VectorSearchService

from .models import (
    CombinedSimilarityResponse,
    SimilarityResult,
    TextSearchRequest,
)

# Initialize logger for the data bank service
logger = get_service_logger("data_bank_service")

vector_service = VectorSearchService()


def upsert_incident(db: DbSession, incident_id: UUID) -> bool:
    """
    Orchestrates the process of embedding and upserting an incident.

    This function takes an incident ID and handles all the necessary data retrieval,
    embedding generation, and vector storage through the VectorSearchService.

    Note: This operation is synchronous for direct API calls. For automatic
    incident operations, async Celery tasks are used.

    Args:
        db (DbSession): Database session for data retrieval.
        incident_id (UUID): The unique identifier for the incident.

    Returns:
        bool: True if the upsert was successful, False otherwise.

    Raises:
        ValueError: If incident not found or embedding fails.
        Exception: For other downstream errors from the embedding or DB layer.
    """
    logger.info(f"Starting upsert process for incident ID: {incident_id}")
    try:
        # Upsert the incident using the vector service (handles everything internally)
        success = vector_service.upsert_incident(db=db, incident_id=incident_id)
        if success:
            logger.info(f"Successfully completed upsert for incident ID: {incident_id}")
        else:
            logger.warning(f"Upsert operation failed for incident ID: {incident_id}")
        return success
    except ValueError as ve:
        logger.error(f"ValueError during upsert for incident {incident_id}: {ve}")
        # Re-raise to be handled by the controller as a client error (e.g., 4xx)
        raise
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during upsert for incident {incident_id}: {e}"
        )
        # Re-raise to be handled by the controller as a server error (e.g., 5xx)
        raise


# Document upsert operations are now handled by knowledge_base service
# This function is deprecated - use knowledge_base endpoints instead


def delete_incident(incident_id: UUID) -> bool:
    """
    Orchestrates the process of deleting an incident's vector from the database.

    This function takes an incident ID and removes the corresponding vector
    embedding from the Qdrant incidents collection.

    Args:
        incident_id (UUID): The unique identifier for the incident to delete.

    Returns:
        bool: True if the deletion was successful, False otherwise.

    Raises:
        Exception: For downstream errors from the vector database layer.
    """
    logger.info(f"Starting deletion process for incident ID: {incident_id}")
    try:
        # Delete the vector from the incidents collection
        success = vector_service.delete_incident_vector(incident_id=incident_id)
        if success:
            logger.info(
                f"Successfully completed deletion for incident ID: {incident_id}"
            )
        else:
            logger.warning(f"Deletion operation failed for incident ID: {incident_id}")
        return success
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during deletion for incident {incident_id}: {e}"
        )
        # Re-raise to be handled by the controller as a server error (e.g., 5xx)
        raise


# Document deletion operations are now handled by knowledge_base service
# This function is deprecated - use knowledge_base endpoints instead


def search_similar_content(
    db: DbSession, text: str, top_k: int = 5
) -> CombinedSimilarityResponse:
    """
    Orchestrates the process of finding similar content across both incidents and documents.

    This function takes any text input and searches both the incidents and documents
    collections to find the most semantically similar content.

    Args:
        db (DbSession): Database session for data retrieval.
        text (str): The text to search for similar content. Can be any
                   content like titles, descriptions, summaries, logs, or error messages.
        top_k (int): The maximum number of similar items to return from each collection.

    Returns:
        CombinedSimilarityResponse: A response containing both similar incidents
                                   and relevant documentations with their scores.

    Raises:
        ValueError: If text is empty or embedding fails.
        Exception: For other downstream errors.
    """
    logger.info(
        f"Starting combined similarity search for text (length={len(text)} chars) with top_k={top_k}"
    )
    try:
        # Use the VectorSearchService for both incident and document searches
        # Search for similar incidents using the search service
        incident_results = vector_service.search_similar_incidents_by_text(
            db=db, text=text, top_k=top_k
        )

        # Search for similar documents using the search service
        document_results = vector_service.search_similar_documents_by_text(
            db=db, text=text, top_k=top_k
        )

        # Format the results to match the response model
        similar_incidents = [
            SimilarityResult(
                payload={
                    "incident_id": (
                        result["incident"].id if result.get("incident") else None
                    ),
                    "incident_title": (
                        result["incident"].title if result.get("incident") else None
                    ),
                },
                score=result["similarity_score"],
            )
            for result in incident_results
            if result.get("incident")
        ]

        relevant_documentations = [
            SimilarityResult(
                payload={
                    "document": result["document"],
                },
                score=result["similarity_score"],
            )
            for result in document_results
            if result.get("document")
        ]

        logger.info(
            f"Combined search completed: {len(similar_incidents)} incidents, {len(relevant_documentations)} documents"
        )

        return CombinedSimilarityResponse(
            similar_incidents=similar_incidents,
            relevant_documentations=relevant_documentations,
        )
    except ValueError as ve:
        logger.error(f"ValueError during combined similarity search: {ve}")
        raise
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during combined similarity search: {e}"
        )
        # Re-raise to be handled by the controller as a server error (e.g., 5xx)
        raise


def search_similar_incidents(
    db: DbSession, text: str, top_k: int = 5
) -> List[SimilarityResult]:
    """
    Orchestrates the process of finding incidents similar to a given text.

    This function is kept for backward compatibility and specific incident-only searches.

    Args:
        db (DbSession): Database session for data retrieval.
        text (str): The text to search for similar incidents.
        top_k (int): The maximum number of similar incidents to return.

    Returns:
        List[SimilarityResult]: A list of similar incidents with their scores.
    """
    logger.info(
        f"Starting incident-only similarity search for text (length={len(text)} chars)"
    )
    try:
        # Use the VectorSearchService for incident search
        # Perform the search using the search service
        search_results = vector_service.search_similar_incidents_by_text(
            db=db, text=text, top_k=top_k
        )

        # Format the results to match the response model
        similar_incidents = [
            SimilarityResult(
                payload={
                    "incident_id": (
                        result["incident"].id if result.get("incident") else None
                    )
                },
                score=result["similarity_score"],
            )
            for result in search_results
            if result.get("incident")
        ]
        logger.info(f"Found {len(similar_incidents)} similar incidents for text search")
        return similar_incidents
    except ValueError as ve:
        logger.error(f"ValueError during incident similarity search: {ve}")
        raise
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during incident similarity search: {e}"
        )
        # Re-raise to be handled by the controller as a server error (e.g., 5xx)
        raise


def get_similar_incidents(
    db: DbSession, incident_id: UUID, top_k: int = 5
) -> List[SimilarityResult]:
    """
    Orchestrates the process of finding incidents similar to a given one.

    This function takes an incident ID and searches the incidents collection
    to find the top 'k' most similar incident vectors.

    NOTE: This function is kept for backward compatibility. For flexible text-based
    searches, use search_similar_incidents() or search_similar_content() instead.

    Args:
        db (DbSession): Database session for data retrieval.
        incident_id (UUID): The unique identifier of the incident to find similarities for.
        top_k (int): The maximum number of similar incidents to return.

    Returns:
        List[SimilarityResult]: A list of similar incidents, including their
                                payload and similarity score.

    Raises:
        ValueError: If incident not found or embedding fails.
        Exception: For other downstream errors.
    """
    logger.info(f"Starting similarity search for incident ID: {incident_id}")
    try:
        # Use the VectorSearchService for incident similarity search
        # Perform the search using the search service by incident ID
        search_results = vector_service.search_similar_incidents_by_id(
            db=db, incident_id=incident_id, top_k=top_k
        )

        # Format the results to match the response model
        similar_incidents = [
            SimilarityResult(
                payload={
                    "incident_id": (
                        result["incident"].id if result.get("incident") else None
                    )
                },
                score=result["similarity_score"],
            )
            for result in search_results
            if result.get("incident")
        ]
        logger.info(
            f"Found {len(similar_incidents)} similar incidents for incident ID: {incident_id}"
        )
        return similar_incidents
    except ValueError as ve:
        logger.error(
            f"ValueError during similarity search for incident {incident_id}: {ve}"
        )
        raise
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during similarity search for incident {incident_id}: {e}"
        )
        # Re-raise to be handled by the controller as a server error (e.g., 5xx)
        raise


def search_similar_documents(db: DbSession, data: TextSearchRequest) -> Dict[str, Any]:
    """
    Search for similar documents using text query.

    This function uses the VectorSearchService to perform semantic similarity search
    across the documents collection and returns the most similar documents.

    Args:
        db (DbSession): Database session for data retrieval.
        data (SimilaritySearchRequest): Search request containing text query and parameters.

    Returns:
        Dict[str, Any]: Search results with similar documents and metadata.

    Raises:
        ValueError: If the search query is invalid.
        Exception: For any other errors during the search process.
    """
    logger.info(
        f"Processing document similarity search for query: '{data.text[:50]}...'"
    )

    try:
        # Initialize the vector search service
        vector_service = VectorSearchService()

        # Perform the similarity search
        results = vector_service.search_similar_documents_by_text(
            db=db, text=data.text, top_k=data.top_k, exclude_ids=data.exclude_ids
        )

        logger.info(
            f"Document similarity search completed successfully, found {len(results)} results"
        )

        return {
            "results": results,
            "query": data.text,
            "total_found": len(results),
            "top_k": data.top_k,
        }

    except ValueError as e:
        logger.warning(f"Invalid search query for document similarity search: {e}")
        # Re-raise ValueError to be handled by the controller as a client error (e.g., 4xx)
        raise
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during document similarity search: {e}"
        )
        # Re-raise to be handled by the controller as a server error (e.g., 5xx)
        raise
